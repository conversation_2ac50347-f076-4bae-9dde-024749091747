
# ============================================================================
# SIMPLE AGENT REGISTRY
# ============================================================================

class AgentRegistry:
    """Simple registry for managing agent configurations."""

    def __init__(self):
        self.agents = {}
        self.tools = {}
        self.nodes = {}  # Store pre-built node functions

    def register_agent(self, name: str, system_prompt: str, tools: list, enabled: bool = True, **kwargs):
        """Register an agent with its configuration."""
        self.agents[name] = {
            "system_prompt": system_prompt,
            "tools": tools,
            "enabled": enabled,
            "type": "react_agent",  # Default type
            **kwargs
        }
        return self

    def register_node(self, name: str, node_function, enabled: bool = True, **kwargs):
        """Register a pre-built node function."""
        self.agents[name] = {
            "node_function": node_function,
            "enabled": enabled,
            "type": "custom_node",
            **kwargs
        }
        return self

    def register_tool(self, name: str, tool):
        """Register a tool by name."""
        self.tools[name] = tool
        return self

    def get_enabled_agents(self):
        """Get all enabled agent configurations."""
        return {name: config for name, config in self.agents.items() if config.get("enabled", True)}

    def get_agent_names(self):
        """Get list of enabled agent names."""
        return list(self.get_enabled_agents().keys())

    def get_react_agents(self):
        """Get only ReAct agents (not custom nodes)."""
        return {name: config for name, config in self.get_enabled_agents().items()
                if config.get("type") == "react_agent"}

    def get_custom_nodes(self):
        """Get only custom node functions."""
        return {name: config for name, config in self.get_enabled_agents().items()
                if config.get("type") == "custom_node"}

    def enable_agent(self, name: str):
        """Enable an agent."""
        if name in self.agents:
            self.agents[name]["enabled"] = True

    def disable_agent(self, name: str):
        """Disable an agent."""
        if name in self.agents:
            self.agents[name]["enabled"] = False

# Create global registry
agent_registry = AgentRegistry()
