from typing import List
from pydantic import BaseModel, Field


class Task(BaseModel):
    """Task to be executed"""
    id: str = Field(description="unique id of the task")
    order: int = Field(description="order of the task")
    task: str = Field(description="task to be executed")
    tool: str = Field(description="tool to be used")
    why: str = Field(description="reason for the task")

# class Plan(BaseModel):
#     """Plan to follow in future"""
#     objective: str = Field(description="objective of the plan")
#     tasks: List[Task] = Field(
#         description="different tasks to execute, should be in sorted order"
#     )